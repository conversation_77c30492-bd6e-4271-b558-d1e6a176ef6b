'use client';
import { useRouter } from 'next/navigation';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';

// Validation schema
const SignupSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email format')
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your password'),
});

export default function SignupPage() {
  const router = useRouter();

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      const { confirmPassword, ...submitData } = values;
      
      const res = await fetch('https://reqres.in/api/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submitData),
      });

      const data = await res.json();

      if (res.ok) {
        alert('Signup successful! Please login.');
        router.push('/login');
      } else {
        setFieldError('email', data.error || 'Signup failed');
      }
    } catch (error) {
      setFieldError('email', 'Network error. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="container mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6">
          <div className="card">
            <div className="card-body">
              <h2 className="card-title text-center mb-4">Sign Up</h2>
              <Formik
                initialValues={{ email: '', password: '', confirmPassword: '' }}
                validationSchema={SignupSchema}
                onSubmit={handleSubmit}
              >
                {({ isSubmitting, errors, touched }) => (
                  <Form>
                    <div className="mb-3">
                      <label htmlFor="email" className="form-label">Email</label>
                      <Field
                        type="email"
                        name="email"
                        className={orm-control }
                        placeholder="Enter your email"
                      />
                      <ErrorMessage name="email" component="div" className="invalid-feedback" />
                    </div>

                    <div className="mb-3">
                      <label htmlFor="password" className="form-label">Password</label>
                      <Field
                        type="password"
                        name="password"
                        className={orm-control }
                        placeholder="Enter your password"
                      />
                      <ErrorMessage name="password" component="div" className="invalid-feedback" />
                    </div>

                    <div className="mb-3">
                      <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
                      <Field
                        type="password"
                        name="confirmPassword"
                        className={orm-control }
                        placeholder="Confirm your password"
                      />
                      <ErrorMessage name="confirmPassword" component="div" className="invalid-feedback" />
                    </div>

                    <button 
                      type="submit" 
                      className="btn btn-success w-100"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Signing up...' : 'Sign Up'}
                    </button>
                  </Form>
                )}
              </Formik>
              
              <div className="text-center mt-3">
                <p>Already have an account? <a href="/login" className="text-decoration-none">Login here</a></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
